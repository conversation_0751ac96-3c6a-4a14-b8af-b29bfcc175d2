# Kubernetes 健康检查实现

## 快速开始

本项目已实现 Kubernetes 健康检查端点，支持 liveness 和 readiness 探针。

### 健康检查端点

- **存活性探针**: `http://localhost:9902/k8s_liveiness`
- **就绪性探针**: `http://localhost:9902/k8s_readiness`

### 配置

健康检查端口在配置文件中设置：

```yaml
server:
  health_port: 9902  # 健康检查端口
```

### Kubernetes 配置

```yaml
livenessProbe:
  httpGet:
    path: /k8s_liveiness
    port: 9902
  initialDelaySeconds: 121
  timeoutSeconds: 10
readinessProbe:
  httpGet:
    path: /k8s_readiness
    port: 9902
  periodSeconds: 10
  timeoutSeconds: 10
```

### 测试

```bash
# 测试健康检查逻辑（无需服务器运行）
python simple_health_test.py

# 测试端点（需要服务器运行）
python test_health_check.py

# 手动测试
curl http://localhost:9902/k8s_liveiness
curl http://localhost:9902/k8s_readiness
```

### 实现文件

- `core/api/health_handler.py` - 健康检查处理器
- `core/http_server.py` - 支持健康检查的HTTP服务器
- `config.yaml` / `config_from_api.yaml` - 配置文件

详细文档请参考 `docs/kubernetes-health-checks.md`。
