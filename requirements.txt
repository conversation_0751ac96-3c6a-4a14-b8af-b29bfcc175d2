# 业务依赖包 - 易变动、业务相关、包较小的依赖
# 首先安装基础依赖: pip install -r requirements-base.txt

# API客户端包
openai==1.61.0
google-generativeai==0.8.4
cozepy==0.12.0
dashscope==1.23.1
baidu-aip==4.16.13

# Web框架和网络包
aiohttp==3.9.3
aiohttp_cors==0.7.0
websockets==14.2
httpx==0.27.2
requests==2.32.3

# 配置和数据处理
pyyml==0.0.2
ruamel.yaml==0.18.10
ormsgpack==1.7.0
Jinja2==3.1.6

# 日志和工具包
loguru==0.7.3
PyJWT==2.8.0
psutil==7.0.0
portalocker==2.10.1
aioconsole==0.8.1

# 业务特定功能包
edge_tts==7.0.0
mem0ai==0.1.62
mcp==1.8.1
mcp-proxy==0.8.0
markitdown==0.1.1
cnlunar==0.2.0

# 网络代理
PySocks==1.7.1